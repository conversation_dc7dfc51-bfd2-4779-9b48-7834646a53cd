{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team"]}, "filesystem-iemss-forex-5min": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team/IBKR-Reports/FOREX/IEMSS Elite FOREX 5-min Scalping"], "disabled": false, "autoApprove": []}, "filesystem-iemss-forex-scalping": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team/IBKR-Reports/FOREX/IEMSS Elite FOREX Scalping"], "disabled": false, "autoApprove": []}, "filesystem-trading-strategies": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team/IBKR-Reports"], "disabled": false, "autoApprove": []}, "filesystem-trusts-converted": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Volumes/MREL I/Transcend/Americana Transcendence  - GGTMW/Master <PERSON>/State National - Jay <PERSON>/SN/Trusts/Trusts Converted"], "disabled": false, "autoApprove": []}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"}, "disabled": false, "autoApprove": []}, "supabase-bteam": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["*"]}, "supabase-billions": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["*"]}, "ibkr-trading": {"command": "/Users/<USER>/IBKR/b-team/ibkr_server.sh", "args": ["--host", "127.0.0.1", "--client-id", "1"], "env": {"IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}, "disabled": false, "autoApprove": ["*"]}, "mcp-pandoc": {"command": "uvx", "args": ["mcp-pandoc"], "disabled": false, "autoApprove": []}, "dalle-mcp": {"command": "node", "args": ["/Users/<USER>/Documents/Cline/MCP/dalle-mcp-server/build/index.js"], "env": {"OPENAI_API_KEY": "********************************************************************************************************************************************************************", "SAVE_DIR": "/Users/<USER>/adverp/docs/dall-e"}, "disabled": false, "autoApprove": ["/Users/<USER>/adverp/docs/dall-e"]}, "fundraise-server": {"command": "node", "args": ["/Users/<USER>/Documents/Cline/MCP/fundraise-server/build/index.js"]}, "github-mcp-server": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}, "disabled": false, "autoApprove": []}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": []}, "sqlite": {"command": "npx", "args": ["-y", "mcp-sqlite"], "disabled": false, "autoApprove": []}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "disabled": false, "autoApprove": []}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "disabled": false, "autoApprove": []}}}