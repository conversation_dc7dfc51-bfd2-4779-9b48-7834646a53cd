#!/usr/bin/env python3
"""
Install Claude Desktop configuration for IBKR MCP Server
"""
import os
import shutil
import json
from pathlib import Path

def install_claude_config():
    """Install the Claude Desktop configuration"""
    
    # Source configuration file
    source_config = Path(__file__).parent / "claude_desktop_config.json"
    
    # Claude Desktop configuration directory
    claude_config_dir = Path.home() / "Library" / "Application Support" / "Claude"
    claude_config_file = claude_config_dir / "claude_desktop_config.json"
    
    print("🔧 Installing Claude Desktop configuration for IBKR MCP Server")
    print(f"📁 Source: {source_config}")
    print(f"📁 Target: {claude_config_file}")
    
    # Create directory if it doesn't exist
    claude_config_dir.mkdir(parents=True, exist_ok=True)
    
    # Read the source configuration
    try:
        with open(source_config, 'r') as f:
            config = json.load(f)
        print("✅ Source configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to read source configuration: {e}")
        return False
    
    # Check if target configuration exists
    if claude_config_file.exists():
        print("⚠️  Existing Claude Desktop configuration found")
        
        # Read existing configuration
        try:
            with open(claude_config_file, 'r') as f:
                existing_config = json.load(f)
            
            # Backup existing configuration
            backup_file = claude_config_file.with_suffix('.json.backup')
            shutil.copy2(claude_config_file, backup_file)
            print(f"💾 Existing configuration backed up to: {backup_file}")
            
            # Merge configurations if needed
            if "mcpServers" in existing_config:
                if "mcpServers" not in config:
                    config["mcpServers"] = {}
                config["mcpServers"].update(existing_config["mcpServers"])
                print("🔄 Merged with existing MCP servers")
                
        except Exception as e:
            print(f"⚠️  Could not read existing configuration: {e}")
    
    # Write the new configuration
    try:
        with open(claude_config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print("✅ Claude Desktop configuration installed successfully")
        
        print("\n📋 Configuration Summary:")
        print(f"   Server Name: ibkr-trading")
        print(f"   Script Path: {config['servers'][0]['ibkr-trading']['args'][0]}")
        print(f"   Environment Variables: {len(config['servers'][0]['ibkr-trading']['env'])} set")
        
        print("\n🔄 Next Steps:")
        print("1. Restart Claude Desktop application")
        print("2. The IBKR trading tools should be available in Claude Desktop")
        print("3. Test the connection using: test_connection()")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to write configuration: {e}")
        return False

def main():
    """Main function"""
    success = install_claude_config()
    if success:
        print("\n🎉 Installation completed successfully!")
    else:
        print("\n💥 Installation failed!")
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
