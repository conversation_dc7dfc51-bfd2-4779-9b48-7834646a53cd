"""
IEMSS Elite FOREX Scalping Engine - Multi-Timeframe
===================================================
Advanced scalping strategy with adaptive timeframe selection
and market microstructure analysis.

Author: IEMSS Trading Desk
Version: 2.0.0
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import talib
from typing import Dict, List, Tuple, Optional, Union
import asyncio
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class TimeFrame(Enum):
    """Supported timeframes for scalping"""
    ONE_MIN = '1min'
    THREE_MIN = '3min'
    FIVE_MIN = '5min'
    TEN_MIN = '10min'
    FIFTEEN_MIN = '15min'
    

class MarketCondition(Enum):
    """Market condition classifications"""
    TRENDING_STRONG = 'trending_strong'
    TRENDING_WEAK = 'trending_weak'
    RANGING = 'ranging'
    BREAKOUT = 'breakout'
    CHOPPY = 'choppy'
    

@dataclass
class AdvancedSignal:
    """Enhanced signal with market microstructure data"""
    timestamp: datetime
    symbol: str
    timeframe: TimeFrame
    direction: str
    entry_price: float
    stop_loss: float
    take_profit_1: float  # First target
    take_profit_2: float  # Second target
    take_profit_3: float  # Third target
    confidence: float
    market_condition: MarketCondition
    order_flow_bias: float  # -1 to 1 (selling to buying pressure)
    liquidity_score: float  # 0 to 1 (low to high liquidity)
    indicators: Dict[str, float]
    pattern: Optional[str] = None
    

class IEMSSMultiTimeframeScalpingEngine:
    """
    Advanced multi-timeframe scalping engine with market microstructure analysis
    """
    
    def __init__(self, symbols: List[str], risk_percent: float = 0.5):
        """
        Initialize the advanced scalping engine
        
        Args:
            symbols: List of forex pairs to trade
            risk_percent: Risk per trade as percentage of account
        """
        self.symbols = symbols
        self.risk_percent = risk_percent
        self.timeframes = [TimeFrame.ONE_MIN, TimeFrame.THREE_MIN, 
                          TimeFrame.FIVE_MIN, TimeFrame.FIFTEEN_MIN]
        
        # Market microstructure parameters
        self.order_flow_window = 20  # Candles to analyze order flow
        self.liquidity_threshold = 0.3  # Minimum liquidity score
        self.correlation_window = 100  # Candles for correlation analysis
        
        # Pattern recognition
        self.patterns = {
            'double_top': self._detect_double_top,
            'double_bottom': self._detect_double_bottom,
            'flag': self._detect_flag_pattern,
            'triangle': self._detect_triangle,
            'head_shoulders': self._detect_head_shoulders
        }
        
        # Session-specific parameters
        self.session_configs = {
            'asian': {'min_atr': 0.0003, 'max_atr': 0.0030, 'min_volume': 5000},
            'london': {'min_atr': 0.0005, 'max_atr': 0.0050, 'min_volume': 20000},
            'newyork': {'min_atr': 0.0004, 'max_atr': 0.0045, 'min_volume': 15000},
            'overlap': {'min_atr': 0.0006, 'max_atr': 0.0060, 'min_volume': 30000}
        }
        
    def analyze_market_condition(self, df: pd.DataFrame) -> MarketCondition:
        """
        Determine current market condition
        
        Args:
            df: Price data
            
        Returns:
            Market condition classification
        """
        if len(df) < 50:
            return MarketCondition.CHOPPY
            
        # Calculate directional movement
        adx = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
        latest_adx = adx.iloc[-1]
        
        # Calculate volatility
        atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
        atr_sma = atr.rolling(window=20).mean()
        volatility_ratio = atr.iloc[-1] / atr_sma.iloc[-1]
        
        # Price position relative to moving averages
        sma_20 = df['close'].rolling(window=20).mean()
        sma_50 = df['close'].rolling(window=50).mean()
        price_above_sma20 = df['close'].iloc[-1] > sma_20.iloc[-1]
        price_above_sma50 = df['close'].iloc[-1] > sma_50.iloc[-1]
        
        # Classify market condition
        if latest_adx > 30 and volatility_ratio > 1.2:
            return MarketCondition.TRENDING_STRONG
        elif latest_adx > 20:
            return MarketCondition.TRENDING_WEAK
        elif volatility_ratio > 1.5:
            return MarketCondition.BREAKOUT
        elif latest_adx < 20 and volatility_ratio < 0.8:
            return MarketCondition.RANGING
        else:
            return MarketCondition.CHOPPY
            
    def calculate_order_flow(self, df: pd.DataFrame) -> float:
        """
        Calculate order flow bias from price and volume
        
        Args:
            df: Price and volume data
            
        Returns:
            Order flow bias (-1 to 1)
        """
        if len(df) < self.order_flow_window:
            return 0.0
            
        recent = df.tail(self.order_flow_window)
        
        # Calculate buying vs selling pressure
        buying_volume = 0
        selling_volume = 0
        
        for idx in range(1, len(recent)):
            if recent['close'].iloc[idx] > recent['close'].iloc[idx-1]:
                buying_volume += recent['volume'].iloc[idx]
            else:
                selling_volume += recent['volume'].iloc[idx]
                
        total_volume = buying_volume + selling_volume
        if total_volume == 0:
            return 0.0
            
        # Calculate order flow bias
        order_flow = (buying_volume - selling_volume) / total_volume
        
        # Apply volume-weighted price analysis
        vwap = (recent['close'] * recent['volume']).sum() / recent['volume'].sum()
        price_vs_vwap = (recent['close'].iloc[-1] - vwap) / vwap
        
        # Combine order flow and price analysis
        combined_bias = 0.7 * order_flow + 0.3 * np.sign(price_vs_vwap) * min(abs(price_vs_vwap) * 100, 1)
        
        return np.clip(combined_bias, -1, 1)
        
    def calculate_liquidity_score(self, df: pd.DataFrame) -> float:
        """
        Calculate market liquidity score
        
        Args:
            df: Price and volume data
            
        Returns:
            Liquidity score (0 to 1)
        """
        if len(df) < 20:
            return 0.0
            
        recent = df.tail(20)
        
        # Volume consistency
        volume_cv = recent['volume'].std() / recent['volume'].mean() if recent['volume'].mean() > 0 else 1
        volume_score = 1 / (1 + volume_cv)
        
        # Spread analysis (high-low range)
        spreads = (recent['high'] - recent['low']) / recent['close']
        spread_score = 1 - min(spreads.mean() * 100, 1)
        
        # Price continuity
        price_gaps = abs(recent['open'] - recent['close'].shift(1)).dropna()
        gap_score = 1 - min(price_gaps.mean() / recent['close'].mean() * 100, 1)
        
        # Combine scores
        liquidity_score = (volume_score + spread_score + gap_score) / 3
        
        return np.clip(liquidity_score, 0, 1)
        
    def select_optimal_timeframe(
        self, 
        market_data: Dict[TimeFrame, pd.DataFrame], 
        symbol: str
    ) -> TimeFrame:
        """
        Select optimal timeframe based on market conditions
        
        Args:
            market_data: Dictionary of dataframes by timeframe
            symbol: Trading symbol
            
        Returns:
            Optimal timeframe for current conditions
        """
        scores = {}
        
        for timeframe, df in market_data.items():
            if len(df) < 100:
                scores[timeframe] = 0
                continue
                
            # Calculate timeframe score
            market_condition = self.analyze_market_condition(df)
            liquidity = self.calculate_liquidity_score(df)
            
            # Base score on market condition
            condition_scores = {
                MarketCondition.TRENDING_STRONG: 0.9,
                MarketCondition.TRENDING_WEAK: 0.7,
                MarketCondition.BREAKOUT: 0.8,
                MarketCondition.RANGING: 0.5,
                MarketCondition.CHOPPY: 0.2
            }
            
            base_score = condition_scores.get(market_condition, 0.5)
            
            # Adjust for timeframe characteristics
            if timeframe == TimeFrame.ONE_MIN:
                # Best for high liquidity, quick scalps
                score = base_score * liquidity * 1.2
            elif timeframe == TimeFrame.THREE_MIN:
                # Good balance of noise and opportunity
                score = base_score * (0.8 + liquidity * 0.2)
            elif timeframe == TimeFrame.FIVE_MIN:
                # Standard scalping timeframe
                score = base_score * 0.9
            elif timeframe == TimeFrame.FIFTEEN_MIN:
                # Better for trending markets
                if market_condition in [MarketCondition.TRENDING_STRONG, MarketCondition.TRENDING_WEAK]:
                    score = base_score * 1.1
                else:
                    score = base_score * 0.7
            else:
                score = base_score
                
            scores[timeframe] = score
            
        # Return timeframe with highest score
        return max(scores.items(), key=lambda x: x[1])[0]
        
    def generate_advanced_signal(
        self, 
        df: pd.DataFrame, 
        symbol: str, 
        timeframe: TimeFrame
    ) -> Optional[AdvancedSignal]:
        """
        Generate advanced trading signal with multiple targets
        
        Args:
            df: Price data
            symbol: Trading symbol
            timeframe: Selected timeframe
            
        Returns:
            Advanced signal or None
        """
        if len(df) < 100:
            return None
            
        # Calculate all indicators
        df = self._calculate_advanced_indicators(df)
        
        # Get market analysis
        market_condition = self.analyze_market_condition(df)
        order_flow = self.calculate_order_flow(df)
        liquidity = self.calculate_liquidity_score(df)
        
        # Check if conditions are suitable
        if liquidity < self.liquidity_threshold:
            return None
            
        # Get current session
        session = self._get_current_session()
        session_config = self.session_configs[session]
        
        # Check volatility requirements
        latest_atr = df['atr'].iloc[-1]
        if not (session_config['min_atr'] <= latest_atr <= session_config['max_atr']):
            return None
            
        # Pattern detection
        detected_pattern = None
        for pattern_name, pattern_func in self.patterns.items():
            if pattern_func(df):
                detected_pattern = pattern_name
                break
                
        # Generate signal based on market condition
        if market_condition == MarketCondition.TRENDING_STRONG:
            signal = self._generate_trend_signal(df, order_flow)
        elif market_condition == MarketCondition.BREAKOUT:
            signal = self._generate_breakout_signal(df, order_flow)
        elif market_condition == MarketCondition.RANGING:
            signal = self._generate_range_signal(df, order_flow)
        else:
            signal = self._generate_standard_signal(df, order_flow)
            
        if signal is None:
            return None
            
        # Calculate dynamic targets based on ATR and market condition
        atr = df['atr'].iloc[-1]
        
        if market_condition == MarketCondition.TRENDING_STRONG:
            tp1_multiplier = 1.5
            tp2_multiplier = 2.5
            tp3_multiplier = 4.0
            sl_multiplier = 1.0
        elif market_condition == MarketCondition.BREAKOUT:
            tp1_multiplier = 2.0
            tp2_multiplier = 3.5
            tp3_multiplier = 5.0
            sl_multiplier = 1.5
        else:
            tp1_multiplier = 1.0
            tp2_multiplier = 1.8
            tp3_multiplier = 2.5
            sl_multiplier = 0.8
            
        # Create advanced signal
        direction, entry_price, confidence = signal
        
        if direction == 'BUY':
            stop_loss = entry_price - (sl_multiplier * atr)
            take_profit_1 = entry_price + (tp1_multiplier * atr)
            take_profit_2 = entry_price + (tp2_multiplier * atr)
            take_profit_3 = entry_price + (tp3_multiplier * atr)
        else:
            stop_loss = entry_price + (sl_multiplier * atr)
            take_profit_1 = entry_price - (tp1_multiplier * atr)
            take_profit_2 = entry_price - (tp2_multiplier * atr)
            take_profit_3 = entry_price - (tp3_multiplier * atr)
            
        return AdvancedSignal(
            timestamp=datetime.now(),
            symbol=symbol,
            timeframe=timeframe,
            direction=direction,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit_1=take_profit_1,
            take_profit_2=take_profit_2,
            take_profit_3=take_profit_3,
            confidence=confidence,
            market_condition=market_condition,
            order_flow_bias=order_flow,
            liquidity_score=liquidity,
            indicators=self._get_indicator_snapshot(df),
            pattern=detected_pattern
        )
        
    def _calculate_advanced_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate advanced technical indicators
        """
        # Standard indicators
        df['ema_5'] = talib.EMA(df['close'], timeperiod=5)
        df['ema_13'] = talib.EMA(df['close'], timeperiod=13)
        df['ema_21'] = talib.EMA(df['close'], timeperiod=21)
        df['ema_50'] = talib.EMA(df['close'], timeperiod=50)
        
        # Momentum indicators
        df['rsi'] = talib.RSI(df['close'], timeperiod=14)
        df['mfi'] = talib.MFI(df['high'], df['low'], df['close'], df['volume'], timeperiod=14)
        df['cci'] = talib.CCI(df['high'], df['low'], df['close'], timeperiod=20)
        
        # Volatility indicators
        df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
        df['natr'] = talib.NATR(df['high'], df['low'], df['close'], timeperiod=14)
        
        # Volume indicators
        df['obv'] = talib.OBV(df['close'], df['volume'])
        df['ad'] = talib.AD(df['high'], df['low'], df['close'], df['volume'])
        
        # Market structure
        df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
        df['r1'] = 2 * df['pivot'] - df['low']
        df['s1'] = 2 * df['pivot'] - df['high']
        
        return df
        
    def _get_current_session(self) -> str:
        """
        Determine current trading session
        """
        hour = datetime.now().hour
        
        if 22 <= hour or hour < 7:  # 10 PM - 7 AM ET
            return 'asian'
        elif 3 <= hour < 12:  # 3 AM - 12 PM ET
            return 'london'
        elif 8 <= hour < 17:  # 8 AM - 5 PM ET
            return 'newyork'
        elif 8 <= hour < 12:  # Overlap period
            return 'overlap'
        else:
            return 'newyork'  # Default
            
    def _detect_double_top(self, df: pd.DataFrame) -> bool:
        """Detect double top pattern"""
        if len(df) < 40:
            return False
            
        # Simplified double top detection
        highs = df['high'].rolling(window=5).max()
        recent_highs = highs.tail(40)
        
        # Find two similar highs with a valley between
        peaks = []
        for i in range(5, len(recent_highs) - 5):
            if (recent_highs.iloc[i] == max(recent_highs.iloc[i-5:i+5])):
                peaks.append((i, recent_highs.iloc[i]))
                
        if len(peaks) >= 2:
            # Check if peaks are similar height (within 0.1%)
            peak1, peak2 = peaks[-2], peaks[-1]
            if abs(peak1[1] - peak2[1]) / peak1[1] < 0.001:
                # Check for valley between peaks
                valley_start = peak1[0] + len(recent_highs) - 40
                valley_end = peak2[0] + len(recent_highs) - 40
                valley = df['low'].iloc[valley_start:valley_end].min()
                
                if valley < peak1[1] * 0.998:  # At least 0.2% below peaks
                    return True
                    
        return False
        
    def _detect_double_bottom(self, df: pd.DataFrame) -> bool:
        """Detect double bottom pattern"""
        if len(df) < 40:
            return False
            
        # Similar to double top but inverted
        lows = df['low'].rolling(window=5).min()
        recent_lows = lows.tail(40)
        
        # Find two similar lows with a peak between
        valleys = []
        for i in range(5, len(recent_lows) - 5):
            if (recent_lows.iloc[i] == min(recent_lows.iloc[i-5:i+5])):
                valleys.append((i, recent_lows.iloc[i]))
                
        if len(valleys) >= 2:
            valley1, valley2 = valleys[-2], valleys[-1]
            if abs(valley1[1] - valley2[1]) / valley1[1] < 0.001:
                peak_start = valley1[0] + len(recent_lows) - 40
                peak_end = valley2[0] + len(recent_lows) - 40
                peak = df['high'].iloc[peak_start:peak_end].max()
                
                if peak > valley1[1] * 1.002:
                    return True
                    
        return False
        
    def _detect_flag_pattern(self, df: pd.DataFrame) -> bool:
        """Detect flag pattern"""
        # Implement flag pattern detection
        return False
        
    def _detect_triangle(self, df: pd.DataFrame) -> bool:
        """Detect triangle pattern"""
        # Implement triangle pattern detection
        return False
        
    def _detect_head_shoulders(self, df: pd.DataFrame) -> bool:
        """Detect head and shoulders pattern"""
        # Implement H&S pattern detection
        return False
        
    def _generate_trend_signal(self, df: pd.DataFrame, order_flow: float) -> Optional[Tuple[str, float, float]]:
        """Generate signal for trending market"""
        latest = df.iloc[-1]
        
        # Trend following with pullback entry
        if (latest['ema_5'] > latest['ema_13'] > latest['ema_21'] and 
            latest['rsi'] < 70 and order_flow > 0.2):
            # Bullish trend continuation
            return ('BUY', latest['close'], 0.75)
        elif (latest['ema_5'] < latest['ema_13'] < latest['ema_21'] and 
              latest['rsi'] > 30 and order_flow < -0.2):
            # Bearish trend continuation
            return ('SELL', latest['close'], 0.75)
            
        return None
        
    def _generate_breakout_signal(self, df: pd.DataFrame, order_flow: float) -> Optional[Tuple[str, float, float]]:
        """Generate signal for breakout conditions"""
        latest = df.iloc[-1]
        
        # Check for breakout with volume confirmation
        if (latest['close'] > latest['r1'] and 
            latest['volume'] > df['volume'].rolling(20).mean().iloc[-1] * 1.5 and
            order_flow > 0.3):
            return ('BUY', latest['close'], 0.80)
        elif (latest['close'] < latest['s1'] and 
              latest['volume'] > df['volume'].rolling(20).mean().iloc[-1] * 1.5 and
              order_flow < -0.3):
            return ('SELL', latest['close'], 0.80)
            
        return None
        
    def _generate_range_signal(self, df: pd.DataFrame, order_flow: float) -> Optional[Tuple[str, float, float]]:
        """Generate signal for ranging market"""
        latest = df.iloc[-1]
        
        # Mean reversion in ranging market
        bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'], timeperiod=20)
        
        if (latest['close'] < bb_lower.iloc[-1] and 
            latest['rsi'] < 30 and order_flow > -0.1):
            return ('BUY', latest['close'], 0.65)
        elif (latest['close'] > bb_upper.iloc[-1] and 
              latest['rsi'] > 70 and order_flow < 0.1):
            return ('SELL', latest['close'], 0.65)
            
        return None
        
    def _generate_standard_signal(self, df: pd.DataFrame, order_flow: float) -> Optional[Tuple[str, float, float]]:
        """Generate standard signal for normal conditions"""
        # Use multiple confirmations
        latest = df.iloc[-1]
        
        buy_signals = 0
        sell_signals = 0
        
        # Check various conditions
        if latest['ema_5'] > latest['ema_21']:
            buy_signals += 1
        else:
            sell_signals += 1
            
        if latest['rsi'] < 50:
            buy_signals += 1
        elif latest['rsi'] > 50:
            sell_signals += 1
            
        if order_flow > 0.1:
            buy_signals += 1
        elif order_flow < -0.1:
            sell_signals += 1
            
        if latest['mfi'] < 40:
            buy_signals += 1
        elif latest['mfi'] > 60:
            sell_signals += 1
            
        # Generate signal if enough confirmations
        if buy_signals >= 3:
            return ('BUY', latest['close'], 0.60 + buy_signals * 0.05)
        elif sell_signals >= 3:
            return ('SELL', latest['close'], 0.60 + sell_signals * 0.05)
            
        return None
        
    def _get_indicator_snapshot(self, df: pd.DataFrame) -> Dict[str, float]:
        """Get current indicator values"""
        latest = df.iloc[-1]
        
        return {
            'ema_5': latest['ema_5'],
            'ema_21': latest['ema_21'],
            'rsi': latest['rsi'],
            'mfi': latest['mfi'],
            'cci': latest['cci'],
            'atr': latest['atr'],
            'obv': latest['obv'],
            'ad': latest['ad']
        }


# Example usage
if __name__ == "__main__":
    symbols = ['EUR.USD', 'GBP.USD', 'USD.JPY']
    engine = IEMSSMultiTimeframeScalpingEngine(symbols)
    
    # Example: Select optimal timeframe and generate signal
    # market_data = load_multi_timeframe_data(symbol)
    # optimal_tf = engine.select_optimal_timeframe(market_data, symbol)
    # signal = engine.generate_advanced_signal(market_data[optimal_tf], symbol, optimal_tf)
